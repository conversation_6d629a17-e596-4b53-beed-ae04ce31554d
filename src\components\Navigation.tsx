"use client";

import Image from 'next/image';
import Link from 'next/link';
import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import { useSession, signIn, signOut } from 'next-auth/react';
import { FaChevronDown, FaHome, FaHeadset, FaUser, FaSignOutAlt } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [activeIndicator, setActiveIndicator] = useState({ left: 0, width: 0, top: 0 });
  const dropdownRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
  const navItemRefs = useRef<{ [key: string]: HTMLElement | null }>({});
  const pathname = usePathname();
  const { data: session, status } = useSession();

  // Track scrolling for navbar appearance change
  useEffect(() => {
    if (window.scrollY > 20) {
      setIsScrolled(true);
    }

    const handleScroll = () => {
      if (window.scrollY > 20) {
        if (!isScrolled) setIsScrolled(true);
      } else {
        if (isScrolled) setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [isScrolled]);

  // Close menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
    setActiveDropdown(null);
  }, [pathname]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!activeDropdown) return;

      const dropdownRef = dropdownRefs.current[activeDropdown];

      if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
        const isClickOnToggle = event.target instanceof Element &&
          event.target.closest(`button[data-dropdown="${activeDropdown}"]`);

        if (!isClickOnToggle) {
          setActiveDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);


  // Determines if a link is active
  const isActive = useCallback((path: string) => {
    if (path === '/') {
      return pathname === '/';
    }
    return pathname === path;
  }, [pathname]);


  // Toggle dropdown menu
  const toggleDropdown = (name: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    setActiveDropdown(activeDropdown === name ? null : name);
  };


  const navLinks = useMemo(() => [
    {
      type: 'link',
      name: 'Forside',
      path: '/',
      icon: <FaHome className="mr-2" />
    },
    {
      type: 'dropdown',
      name: 'Kontakt',
      items: [
        { name: 'Support', path: '/support' },
        { name: 'Bliv Partner', path: '/bliv-partner' }
      ]
    }
  ], []);

  // Determine which top-level item (link or dropdown) should be "active" for the indicator
  const getActiveItemKey = useCallback(() => {
    // Homepage doesn't highlight anything
    if (pathname === '/') {
      return '';
    }

    // First look for exact link match
    for (let i = 0; i < navLinks.length; i++) {
      const item = navLinks[i];
      if (item.type === 'link' && item.path && isActive(item.path)) {
        return `link-${i}`;
      }
    }

    return '';
  }, [pathname, isActive, navLinks]);

  // Update the active indicator position based on the current pathname
  const updateActiveIndicator = useCallback(() => {
    const activeKey = getActiveItemKey();

    if (!activeKey) {
      setActiveIndicator({ left: 0, width: 0, top: 0 });
      return;
    }

    const activeElement = navItemRefs.current[activeKey];

    if (activeElement) {
      const rect = activeElement.getBoundingClientRect();
      setActiveIndicator({
        left: rect.left,
        width: rect.width,
        top: rect.top + rect.height - 2
      });
    }
  }, [getActiveItemKey]);

  // Update active indicator position
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let updateTimeout: NodeJS.Timeout | null = null;
    const debouncedUpdate = () => {
      if (updateTimeout) clearTimeout(updateTimeout);
      updateTimeout = setTimeout(() => {
        updateActiveIndicator();
      }, 100);
    };

    updateActiveIndicator();

    const resizeObserver = new ResizeObserver(() => {
      debouncedUpdate();
    });

    resizeObserver.observe(document.body);

    return () => {
      if (updateTimeout) clearTimeout(updateTimeout);
      resizeObserver.disconnect();
    };
  }, [pathname, isScrolled, updateActiveIndicator]);


  const isTextDark = isScrolled || pathname === '/login';

  return (
    <>
      {/* Fixed navbar */}
      <nav className={`fixed w-full z-50 transition-all duration-350 ease-in-out ${
        isScrolled
          ? 'bg-white/95 backdrop-blur-sm text-gray-800 shadow-lg py-2 sm:py-2'
          : 'bg-transparent text-white py-3 sm:py-4'
      }`}>
        <div className="container mx-auto px-4 sm:px-6">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <Link href="/" className="flex items-center group z-10" aria-label="Myckas Plugins home">
              <div className="w-8 h-8 mr-2 sm:mr-3 relative">
                <Image
                  src="/next.svg"
                  alt="Myckas Plugins logo"
                  fill
                  sizes="500px"
                  className="object-contain rounded-lg"
                  priority
                />
              </div>
              <span className={`text-lg sm:text-xl font-bold transition-all duration-500 ease-in-out group-hover:translate-x-1 ${
                isTextDark ? 'text-gray-800' : 'text-white'
              }`}>
                Myckas Plugins
              </span>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1 lg:space-x-3 z-10">
              <div className="flex space-x-1 lg:space-x-3 relative">
                {/* Animated active indicator */}
                <motion.div
                  className={`absolute h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} rounded-full z-0`}
                  initial={false}
                  animate={{
                    left: activeIndicator.left,
                    width: activeIndicator.width,
                    top: activeIndicator.top,
                  }}
                  transition={{
                    type: "spring",
                    stiffness: 350,
                    damping: 30
                  }}
                />

                {navLinks.map((item, index) => {
                  if (item.type === 'link') {
                    const key = `link-${index}`;
                    const path = item.path;
                    return (
                      <Link
                        key={key}
                        href={path || ''}
                        ref={(el) => { navItemRefs.current[key] = el; }}
                        className={`font-medium relative px-2 lg:px-3 py-2 rounded-md transition-all duration-300 ease-in-out text-sm lg:text-base ${
                          path && isActive(path)
                            ? (isTextDark ? 'text-blue-600' : 'text-white font-bold')
                            : (isTextDark ? 'text-gray-800 hover:text-blue-600 hover:bg-blue-50' : 'text-white hover:text-blue-200 hover:bg-white/10')
                        } group`}
                      >
                        <span className="flex items-center">
                          <span className="md:hidden lg:inline-block">{item.icon}</span>
                          {item.name}
                        </span>
                        <span className={`absolute bottom-0 left-0 h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} transition-all duration-300 ease-in-out ${
                          path && isActive(path) ? 'w-full' : 'w-0 group-hover:w-full'
                        }`}></span>
                      </Link>
                    );
                  }

                  if (item.type === 'dropdown' && item.name === 'Kontakt') {
                    return (
                      <div key={`dropdown-${index}`} className="relative z-50 group">
                        <button
                          onClick={(e) => toggleDropdown('contactMenu', e)}
                          data-dropdown="contactMenu"
                          ref={(el) => { navItemRefs.current['dropdown-Kontakt'] = el; }}
                          className={`font-medium relative px-2 lg:px-3 py-2 rounded-md transition-all duration-300 ease-in-out text-sm lg:text-base flex items-center ${
                            isTextDark ? 'text-gray-800 hover:text-blue-600 hover:bg-blue-50' : 'text-white hover:text-blue-200 hover:bg-white/10'
                          }`}
                        >
                          <span className="flex items-center">
                            <FaHeadset className="mr-2" />
                            Kontakt
                          </span>
                          <motion.div
                            animate={{ rotate: activeDropdown === 'contactMenu' ? 180 : 0 }}
                            transition={{ duration: 0.2, type: "spring", stiffness: 200 }}
                            className="ml-1"
                          >
                            <FaChevronDown className={`h-3 w-3 ${isTextDark ? 'text-gray-600' : 'text-white'}`} />
                          </motion.div>
                          {/* underline */}
                          <span className={`pointer-events-none absolute bottom-0 left-0 h-0.5 ${isTextDark ? 'bg-blue-600' : 'bg-white'} transition-all duration-300 ease-in-out ${
                            getActiveItemKey() === 'dropdown-Kontakt' ? 'w-full' : 'w-0 group-hover:w-full'
                          }`} />
                        </button>
                        <AnimatePresence>
                          {activeDropdown === 'contactMenu' && (
                            <motion.div
                              initial={{ opacity: 0, y: 8, scale: 0.98 }}
                              animate={{ opacity: 1, y: 0, scale: 1 }}
                              exit={{ opacity: 0, y: 8, scale: 0.98 }}
                              transition={{ duration: 0.18 }}
                              className="absolute left-0 mt-2 w-56 bg-white rounded-xl shadow-lg overflow-hidden z-50 border border-blue-100"
                              ref={(el) => { dropdownRefs.current['contactMenu'] = el; }}
                            >
                              <div className="py-2">
                                <Link
                                  href="/support"
                                  className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
                                  onClick={() => setActiveDropdown(null)}
                                >
                                  Support
                                </Link>
                                <Link
                                  href="/bliv-partner"
                                  className="flex items-center px-4 py-2.5 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
                                  onClick={() => setActiveDropdown(null)}
                                >
                                  Bliv Partner
                                </Link>
                              </div>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    );
                  }

                  return null;
                })}
              </div>

              {/* Auth button */}
              <div className="group relative ml-1 sm:ml-2">
                {status === 'loading' ? (
                  <div className="bg-gray-300 text-gray-600 px-4 sm:px-5 py-2 rounded-full flex items-center justify-center">
                    <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2"></div>
                    <span className="text-sm font-medium">Indlæser...</span>
                  </div>
                ) : session ? (
                  // Additional check to ensure session is valid
                  session.user && session.user.email ? (
                    <button
                      onClick={() => signOut({ callbackUrl: '/' })}
                      className="group relative"
                    >
                      <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-red-600 to-red-500 rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="relative bg-gradient-to-b from-red-600 to-red-500 text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg">
                          <div className="w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2">
                            {session.user?.image ? (
                              <Image
                                  src={session.user.image || '/default-avatar.png'}
                                  alt={session.user.name || 'User'}
                                  width={16}
                                  height={16}
                                  className="rounded-full"
                                />
                            ) : (
                              <FaUser className="w-4 h-4" />
                            )}
                          </div>
                          <div className="flex items-center">
                            <span className="text-sm font-medium mr-2">{session.user?.name || 'Bruger'}</span>
                            <FaSignOutAlt className="w-4 h-4" />
                          </div>
                        </div>
                      </div>
                    </button>
                  ) : (
                    // If session exists but no user email, show login button
                    <button
                      onClick={() => signIn('discord')}
                      className="group relative"
                    >
                      <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div className="relative bg-gradient-to-b from-blue-600 to-blue-500 text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg">
                          <div className="w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2">
                            <FaUser className="w-4 h-4" />
                          </div>
                          <span className="text-sm font-medium">Log ind med Discord</span>
                        </div>
                      </div>
                    </button>
                  )
                ) : (
                  <button
                    onClick={() => signIn('discord')}
                    className="group relative"
                  >
                    <div className="relative group">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 rounded-full blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="relative bg-gradient-to-b from-blue-600 to-blue-500 text-white px-4 sm:px-5 py-2 rounded-full flex items-center justify-center border-none transition-all duration-300 hover:scale-105 hover:shadow-lg">
                        <div className="w-8 h-8 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center mr-2">
                          <FaUser className="w-4 h-4" />
                        </div>
                        <span className="text-sm font-medium">Log ind med Discord</span>
                      </div>
                    </div>
                  </button>
                )}
              </div>
            </div>

            {/* Mobile Menu Button */}
            <button
              className={`md:hidden relative z-60 focus:outline-none transition-all duration-300 ease-in-out p-2 -mr-2`}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'transform rotate-45 translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'opacity-0 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
              <div className={`w-6 sm:w-7 h-0.5 my-1.5 transition-all duration-300 ease-in-out ${
                isMenuOpen ? 'transform -rotate-45 -translate-y-2 bg-gray-800' : isTextDark ? 'bg-gray-800' : 'bg-white'
              }`}></div>
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            transition={{ duration: 0.25, ease: "easeInOut" }}
            className="fixed inset-0 bg-white z-40 overflow-y-auto"
          >
            <div className="container mx-auto px-4 pt-20 sm:pt-24 pb-8 h-full flex flex-col">
              <div className="flex flex-col text-base sm:text-lg">
                {/* Mobile navigation */}
                <nav className="w-full">
                  <ul className="divide-y divide-gray-100 rounded-xl overflow-hidden bg-white">
                    {navLinks.map((item, index) => {
                      if (item.type === 'link') {
                        const path = item.path;
                        const active = path && isActive(path);
                        return (
                          <li key={`mobile-link-${index}`}>
                            <Link
                              href={path || ''}
                              onClick={() => setIsMenuOpen(false)}
                              className={`flex items-center gap-3 px-4 py-3 transition-colors ${
                                active
                                  ? 'text-blue-700 bg-blue-50 font-semibold'
                                  : 'text-gray-800 hover:bg-gray-50'
                              }`}
                            >
                              <span className={`inline-flex h-6 w-6 items-center justify-center rounded-md ${
                                active ? 'text-blue-600' : 'text-gray-500'
                              }`}>
                                <span className="leading-none">{item.icon}</span>
                              </span>
                              <span>{item.name}</span>
                            </Link>
                          </li>
                        );
                      }

                      if (item.type === 'dropdown' && item.name === 'Kontakt') {
                        const isOpen = activeDropdown === 'mobileContact';
                        return (
                          <li key={`mobile-dropdown-${index}`} className="bg-white">
                            <button
                              onClick={() => setActiveDropdown(isOpen ? null : 'mobileContact')}
                              className="w-full flex items-center justify-between px-4 py-3 text-gray-800 hover:bg-gray-50"
                              aria-expanded={isOpen}
                              aria-controls="mobile-contact-panel"
                            >
                              <span className="flex items-center gap-3">
                                <span className="inline-flex h-6 w-6 items-center justify-center text-gray-500">
                                  <FaHeadset className="h-4 w-4" />
                                </span>
                                <span>Kontakt</span>
                              </span>
                              <motion.span
                                animate={{ rotate: isOpen ? 180 : 0 }}
                                transition={{ duration: 0.2 }}
                                className="text-gray-500"
                              >
                                <FaChevronDown className="h-4 w-4" />
                              </motion.span>
                            </button>
                            <AnimatePresence initial={false}>
                              {isOpen && (
                                <motion.div
                                  id="mobile-contact-panel"
                                  initial={{ height: 0, opacity: 0 }}
                                  animate={{ height: 'auto', opacity: 1 }}
                                  exit={{ height: 0, opacity: 0 }}
                                  transition={{ duration: 0.2 }}
                                  className="px-2 pb-2"
                                >
                                  <ul className="rounded-lg border border-gray-100 overflow-hidden">
                                    <li>
                                      <Link
                                        href="/support"
                                        className="block px-3 py-2 text-gray-700 hover:bg-gray-50"
                                        onClick={() => { setIsMenuOpen(false); setActiveDropdown(null); }}
                                      >
                                        Support
                                      </Link>
                                    </li>
                                    <li>
                                      <Link
                                        href="/bliv-partner"
                                        className="block px-3 py-2 text-gray-700 hover:bg-gray-50"
                                        onClick={() => { setIsMenuOpen(false); setActiveDropdown(null); }}
                                      >
                                        Bliv Partner
                                      </Link>
                                    </li>
                                  </ul>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </li>
                        );
                      }

                      return null;
                    })}
                  </ul>
                </nav>

                {/* Mobile CTA */}
                <div className="mt-auto pb-4 sm:pb-8">
                  {status === 'loading' ? (
                    <div className="block w-full py-2.5 sm:py-3 px-4 bg-gray-300 text-gray-600 text-center rounded-lg font-bold text-base sm:text-lg transition-all duration-300 ease-in-out mb-6 flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2"></div>
                      <span>Indlæser...</span>
                    </div>
                  ) : session ? (
                    // Additional check to ensure session is valid
                    session.user && session.user.email ? (
                      <button
                        onClick={() => {
                          signOut({ callbackUrl: '/' });
                          setIsMenuOpen(false);
                        }}
                        className="block w-full py-2.5 sm:py-3 px-4 bg-red-600 text-white text-center rounded-lg font-bold text-base sm:text-lg transition-all duration-300 ease-in-out hover:bg-red-700 shadow-md hover:shadow-lg transform hover:scale-[1.02] mb-6 flex items-center justify-center"
                      >
                        <FaSignOutAlt className="mr-2 text-lg" />
                        Log ud
                      </button>
                    ) : (
                      // If session exists but no user email, show login button
                      <button
                        onClick={() => {
                          signIn('discord');
                          setIsMenuOpen(false);
                        }}
                        className="block w-full py-2.5 sm:py-3 px-4 bg-blue-600 text-white text-center rounded-lg font-bold text-base sm:text-lg transition-all duration-300 ease-in-out hover:bg-blue-700 shadow-md hover:shadow-lg transform hover:scale-[1.02] mb-6 flex items-center justify-center"
                      >
                        <FaUser className="mr-2 text-lg" />
                        Log ind med Discord
                      </button>
                    )
                  ) : (
                    <button
                      onClick={() => {
                        signIn('discord');
                        setIsMenuOpen(false);
                      }}
                      className="block w-full py-2.5 sm:py-3 px-4 bg-blue-600 text-white text-center rounded-lg font-bold text-base sm:text-lg transition-all duration-300 ease-in-out hover:bg-blue-700 shadow-md hover:shadow-lg transform hover:scale-[1.02] mb-6 flex items-center justify-center"
                    >
                      <FaUser className="mr-2 text-lg" />
                      Log ind med Discord
                    </button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}