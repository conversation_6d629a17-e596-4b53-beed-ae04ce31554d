# Discord Bot Setup Instructions

To use Discord authentication with your Next.js application, you need to create a Discord application and bot. Here's how to set it up:

## 1. Create a Discord Application

1. Go to the [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" in the top right corner
3. Give your application a name (e.g., "<PERSON><PERSON>as Plugins")
4. Click "Create"

## 2. Create a Bot User

1. In your application dashboard, go to the "Bot" tab
2. Click "Add Bot"
3. Confirm by clicking "Yes, do it!"

## 3. Get OAuth2 Credentials

1. Go to the "OAuth2" → "General" tab
2. Copy the **Client ID** and add it to your `.env` file as `DISCORD_CLIENT_ID`
3. Generate a **Client Secret** and add it to your `.env` file as `DISCORD_CLIENT_SECRET`

## 4. Configure OAuth2 Redirect URL

1. In the "OAuth2" → "General" tab, find the "Redirects" section
2. Add your callback URL: `http://localhost:3000/api/auth/callback/discord`
   - For production, use: `https://yourdomain.com/api/auth/callback/discord`
3. Save changes

## 5. Enable Bot Scopes

1. Go to the "OAuth2" → "URL Generator" tab
2. Select the following scopes:
   - `identify` - To get user information
   - `email` - To get user email (optional)
   - `guilds` - To get user guild information (optional)
3. Copy the generated URL and use it for testing if needed

## 6. Bot Permissions (Optional)

If you want your bot to interact with users, you can:
1. Go to the "Bot" tab
2. Under "Privileged Gateway Intents", enable:
   - SERVER MEMBERS INTENT
   - MESSAGE CONTENT INTENT
3. Click "Save Changes"

## 7. Invite the Bot to Your Server (Optional)

If you want to test the bot in a specific server:
1. Go to the "OAuth2" → "URL Generator" tab
2. Select the bot scope
3. Select the necessary permissions
4. Use the generated URL to invite the bot to your server

## Required Environment Variables

Make sure your `.env` file contains:

```env
MONGODB_URI=your_mongodb_connection_string
NEXTAUTH_URL=http://localhost:3000  # or your production URL
NEXTAUTH_SECRET=your_random_secret_here
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_CLIENT_SECRET=your_discord_client_secret_here
```

## What You Need from Your Discord Bot

For authentication to work, you only need:
1. **Client ID** - Found in your application dashboard
2. **Client Secret** - Found in your application dashboard (generate if not created)
3. **Redirect URI** - Set to `http://localhost:3000/api/auth/callback/discord` for development

You don't actually need to run a bot for authentication to work. The Discord OAuth2 flow only requires the application credentials and proper redirect URI configuration.

## Testing

After setting up, restart your Next.js development server and try to log in with Discord. You should be redirected to Discord for authorization, then back to your application.