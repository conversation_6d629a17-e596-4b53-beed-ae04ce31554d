@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
  --primary: #1e40af;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #171717;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #171717;
  --destructive: #ef4444;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #3b82f6;
  --chart-1: #3b82f6;
  --chart-2: #10b981;
  --chart-3: #f59e0b;
  --chart-4: #8b5cf6;
  --chart-5: #ec4899;
  --sidebar: #ffffff;
  --sidebar-foreground: #171717;
  --sidebar-primary: #1e40af;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f3f4f6;
  --sidebar-accent-foreground: #171717;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #3b82f6;
  --radius: 0.625rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --card: #1f2937;
    --card-foreground: #f9fafb;
    --popover: #1f2937;
    --popover-foreground: #f9fafb;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #374151;
    --secondary-foreground: #f9fafb;
    --muted: #374151;
    --muted-foreground: #d1d5db;
    --accent: #374151;
    --accent-foreground: #f9fafb;
    --destructive: #dc2626;
    --border: #374151;
    --input: #374151;
    --ring: #60a5fa;
    --chart-1: #60a5fa;
    --chart-2: #34d399;
    --chart-3: #fbbf24;
    --chart-4: #a78bfa;
    --chart-5: #f472b6;
    --sidebar: #1f2937;
    --sidebar-foreground: #f9fafb;
    --sidebar-primary: #60a5fa;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #374151;
    --sidebar-accent-foreground: #f9fafb;
    --sidebar-border: #374151;
    --sidebar-ring: #60a5fa;
  }
}

/* Enable smooth scrolling for in-page anchor links */
html:focus-within {
  scroll-behavior: smooth;
}
/* Fallback for general usage */
html {
  scroll-behavior: smooth;
}

/* Arrow animation for links */
.arrow-wrapper {
  display: inline-flex;
  align-items: center;
  transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper {
  transform: translateX(8px);
}

.arrow-wrapper svg {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.group:hover .arrow-wrapper svg {
  transform: scale(1.1);
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover animations */
.transition-transform {
  transition: transform 0.3s ease;
}

.hover\:transform:hover {
  transform: scale(1.05);
}

/* Blob animation */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Gradient text animation */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

/* Card hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
