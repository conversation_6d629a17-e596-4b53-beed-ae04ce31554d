"use client";

import { useState } from "react";
import LicenseStats from "@/components/LicenseStats";
import LicenseCard from "@/components/LicenseCard";
import CreateLicenseModal from "@/components/CreateLicenseModal";
import { License } from "@/types/license";

const mockLicenses: License[] = [
  {
    id: "1",
    pluginName: "Prison Cells",
    pluginId: "cells",
    licenseKey: "MYCKA-CELLS-ABCD-1234-EFGH",
    createdAt: "2024-01-15",
    expiresAt: "2025-01-15",
    status: "active"
  }
];

const mockPlugins = [
  {
    id: "cells",
    name: "Prison Cells"
  }
];

export default function DashboardPage() {
  const [licenses, setLicenses] = useState<License[]>(mockLicenses);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleDeleteLicense = (licenseId: string) => {
    setLicenses(licenses.filter(license => license.id !== licenseId));
  };

  const handleCreateLicense = (pluginId: string) => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const newLicense: License = {
        id: Date.now().toString(),
        pluginName: mockPlugins.find(p => p.id === pluginId)?.name || "",
        pluginId: pluginId,
        licenseKey: `MYCKA-${pluginId.toUpperCase()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
        createdAt: new Date().toISOString().split('T')[0],
        expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: "active"
      };
      
      setLicenses([...licenses, newLicense]);
      setShowCreateModal(false);
      setIsLoading(false);
    }, 1500);
  };

  const canCreateLicense = licenses.length < 1;

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
          <p className="text-gray-600">Administrer dine licenser og plugins her</p>
        </div>

        <LicenseStats licenses={licenses} />

        <div className="rounded-xl border border-blue-100 bg-white text-gray-800 shadow-lg shadow-blue-50/50 transition-all duration-300 hover:shadow-xl hover:shadow-blue-100/50 hover:border-blue-200 backdrop-blur-sm mb-8">
          <div className="px-6 py-4 border-b border-blue-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold leading-tight tracking-tight text-gray-800 transition-colors duration-300">Mine Licenser</h2>
              {canCreateLicense && (
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="group relative bg-gradient-to-b from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg font-bold overflow-hidden transition-all duration-500 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    Opret Ny License
                    <svg className="w-4 h-4 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                </button>
              )}
              {!canCreateLicense && (
                <span className="text-gray-500 text-sm">
                  Du kan kun have én licens ad gangen
                </span>
              )}
            </div>
          </div>
          <div className="p-6">
            {licenses.length === 0 ? (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Ingen licenser</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Du har endnu ikke nogen licenser.
                </p>
                {canCreateLicense && (
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="mt-4 group relative bg-gradient-to-b from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg font-bold overflow-hidden transition-all duration-500 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105"
                  >
                    <span className="relative z-10 flex items-center justify-center">
                      Opret din første license
                      <svg className="w-4 h-4 ml-2 transform transition-all duration-500 ease-[cubic-bezier(0.68,-0.55,0.27,1.55)] group-hover:translate-x-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                  </button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {licenses.map((license) => (
                  <LicenseCard
                    key={license.id}
                    license={license}
                    onDelete={handleDeleteLicense}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        <CreateLicenseModal
          plugins={mockPlugins}
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onCreate={handleCreateLicense}
          isLoading={isLoading}
        />
      </div>
    </main>
  );
}