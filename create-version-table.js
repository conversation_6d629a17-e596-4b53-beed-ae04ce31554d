const fs = require('fs');
const path = require('path');
const { MongoClient } = require('mongodb');

// Default plugin name
const PLUGIN_NAME = 'cellesystem';

// Read the .env file to get MongoDB URI
const envPath = path.join(__dirname, '.env');
const envContent = fs.readFileSync(envPath, 'utf8');

// Extract MongoDB URI from .env
const mongoUriMatch = envContent.match(/MONGODB_URI=(.+)/);
if (!mongoUriMatch) {
  console.error('MONGODB_URI not found in .env file');
  process.exit(1);
}

const mongoUri = mongoUriMatch[1];

// Read version data from .env (assuming it's in the format you provided)
const versionMatch = envContent.match(/LATEST_VERSION="(.+)"/);
const latestVersion = versionMatch ? versionMatch[1] : '1.3.0'; // Default version if not found

async function createVersionTable() {
  const client = new MongoClient(mongoUri);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB Atlas');
    
    const db = client.db(); // Uses the database from the connection string
    const collection = db.collection('versions');
    
    // Check if the collection already exists
    const collections = await db.listCollections().toArray();
    const collectionExists = collections.some(col => col.name === 'versions');
    
    if (!collectionExists) {
      // Create the collection
      await db.createCollection('versions');
      console.log('Created "versions" collection');
    }
    
    // Insert or update the version data with plugin name
    const versionData = {
      [PLUGIN_NAME]: {
        latest: latestVersion,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    };
    
    const result = await collection.updateOne(
      { _id: 'versions' }, // Use a fixed document ID
      { $set: versionData },
      { upsert: true } // Insert if not exists, update if exists
    );
    
    console.log(`Version document ${result.upsertedId ? 'inserted' : 'updated'}`);
    console.log(`Current version for ${PLUGIN_NAME}:`, latestVersion);
    
  } catch (error) {
    console.error('Error creating version table:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB Atlas');
  }
}

// Create the table/document
createVersionTable();