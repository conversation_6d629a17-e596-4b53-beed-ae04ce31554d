import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import LayoutWrapper from "@/components/LayoutWrapper";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Myckas Plugins",
  description: "Minecraft server plugins af Myckas",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="da">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white`}>
        <LayoutWrapper>
          <main className="flex-grow">{children}</main>
          <footer className="bg-white py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center text-gray-600">
                <p>&copy; 2025 Myckas Plugins. Alle rettigheder forbeholdes.</p>
                <p className="mt-1 text-sm">Minecraft plugins af høj kvalitet</p>
              </div>
            </div>
          </footer>
        </LayoutWrapper>
      </body>
    </html>
  );
}
