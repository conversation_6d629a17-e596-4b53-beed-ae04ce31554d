"use client";

interface SmoothScrollButtonProps {
  targetId: string;
  children: React.ReactNode;
  className?: string;
}

export default function SmoothScrollButton({ targetId, children, className }: SmoothScrollButtonProps) {
  const handleClick = () => {
    const element = document.getElementById(targetId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <button
      onClick={handleClick}
      className={className}
    >
      {children}
    </button>
  );
}