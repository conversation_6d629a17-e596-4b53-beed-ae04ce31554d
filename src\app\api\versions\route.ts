import { NextResponse } from 'next/server';
import clientPromise from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// Define a type for the versions document
interface VersionsDocument {
  _id: string;
  [key: string]: string | number | boolean | object | null;
}

export async function GET() {
  try {
    const client = await clientPromise;
    const db = client.db();
    const collection = db.collection('versions');
    
    // Get the raw versions document
    const versions = await collection.findOne<VersionsDocument>({ _id: new ObjectId('versions') }, { projection: { _id: 0 } });
    
    if (!versions) {
      return NextResponse.json({ error: 'Versions document not found' }, { status: 404 });
    }
    
    // Return the raw versions table data
    return NextResponse.json(versions);
  } catch (error) {
    console.error('Error fetching versions:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}