import Link from "next/link";
import SmoothScrollButton from "@/components/SmoothScrollButton";

interface Plugin {
  id: string;
  name: string;
  description: string;
  version: string;
  mcVersion: string;
  image: string;
  features: string[];
  downloads: number;
  rating: number;
}


const plugins: Plugin[] = [
  {
    id: "cells",
    name: "Prison Cells",
    description: "Et plugin til at administrere fængselsceller på din Minecraft server. Perfekt til prison servere!",
    version: "1.0.0",
    mcVersion: "1.8",
    image: "/api/placeholder/300/200",
    features: [
      "Automatiseret celle administration",
      "Integration med economy plugins",
      "Customizable cell designs",
      "Full support for 1.8+ versions"
    ],
    downloads: 0,
    rating: 0
  }
];

// const features: Feature[] = [
//   {
//     title: "High Performance",
//     description: "Optimeret til store servere med minimal resource forbrug og maksimal effektivitet.",
//     icon: "⚡",
//     color: "from-blue-500 to-cyan-500"
//   },
//   {
//     title: "Sikkerhed",
//     description: "Beskyttet mod exploits med avancerede sikkerhedsforanstaltninger.",
//     icon: "🔒",
//     color: "from-green-500 to-emerald-500"
//   },
//   {
//     title: "Brugervenlighed",
//     description: "Intuitivt interface og nem konfiguration for alle niveauer af brugere.",
//     icon: "🎯",
//     color: "from-purple-500 to-pink-500"
//   },
//   {
//     title: "24/7 Support",
//     description: "Professionel support tilgængelig døgnet rundt for alle vores plugins.",
//     icon: "💬",
//     color: "from-orange-500 to-red-500"
//   }
// ];

// const testimonials: Testimonial[] = [];

export default function Home() {
  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 via-gray-50 to-gray-100">
      {/* Modern Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>
        
        <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
          <div className="text-center lg:text-left max-w-5xl mx-auto">
            <div className="inline-flex items-center gap-2 mb-6 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
              <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></span>
              <span className="text-sm font-medium">Professionelle Minecraft Plugins</span>
            </div>
            
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 leading-tight">
              Transformér din <span className="bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">Minecraft Server</span>
            </h1>
            
            <p className="text-xl sm:text-2xl text-blue-100 mb-8 max-w-3xl">
              Professionelle plugins udviklet med fokus på stabilitet, performance og
              <span className="font-semibold text-cyan-300"> exceptionel brugeroplevelse</span>
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-12">
              <SmoothScrollButton
                targetId="populaere-plugins"
                className="group relative bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-cyan-500/30 hover:scale-105 cursor-pointer"
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  Udforsk Plugins
                  <svg className="w-5 h-5 transform transition-all duration-500 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </SmoothScrollButton>
              
              <Link href="/dashboard" className="group relative bg-transparent border-2 border-white/30 text-white px-8 py-4 rounded-xl font-bold text-lg overflow-hidden transition-all duration-500 hover:bg-white hover:text-blue-900 hover:shadow-2xl hover:shadow-white/20">
                <span className="relative z-10 flex items-center justify-center gap-2">
                  Dashboard
                  <svg className="w-5 h-5 transform transition-all duration-500 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </Link>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400">0</div>
                <div className="text-sm text-blue-200">Total Downloads</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400">1</div>
                <div className="text-sm text-blue-200">Plugins</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400">0★</div>
                <div className="text-sm text-blue-200">Gennemsnitlig Rating</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-cyan-400">24/7</div>
                <div className="text-sm text-blue-200">Support</div>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Plugins Showcase */}
      <section id="populaere-plugins" className="py-20 bg-gradient-to-b from-gray-50 to-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-flex items-center gap-2 rounded-full border border-purple-200 bg-purple-50 px-4 py-2 text-sm font-medium text-purple-700 mb-4">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z" />
              </svg>
              Populære Plugins
            </span>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                Vores Plugins
              </span>
              <br />
              <span className="text-gray-800">Udviklet til Perfektion</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Opdag vores samling af high-quality plugins designet til at forbedre din server
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {plugins.map((plugin) => (
              <div
                key={plugin.id}
                className="group relative h-full bg-white rounded-2xl border border-gray-200 overflow-hidden hover:border-blue-300 transition-all duration-500 hover:shadow-2xl hover:-translate-y-1"
              >
                {/* Plugin Header */}
                <div className="h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center relative overflow-hidden">
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="relative z-10 text-center">
                    <div className="text-4xl mb-2">⚡</div>
                    <h3 className="text-2xl font-bold text-white">{plugin.name}</h3>
                  </div>
                  <div className="absolute top-4 right-4 bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                    <span className="text-white font-semibold">v{plugin.version}</span>
                  </div>
                </div>
                
                {/* Plugin Content */}
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-1">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-4 h-4 ${i < Math.floor(plugin.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                      <span className="text-sm text-gray-600 ml-1">{plugin.rating}</span>
                    </div>
                    <span className="text-sm text-gray-500 flex items-center gap-1">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                      {plugin.downloads.toLocaleString()}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-6 leading-relaxed">{plugin.description}</p>
                  
                  {/* Features */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold text-gray-900 mb-2">Kernefunktioner:</h4>
                    <div className="flex flex-wrap gap-2">
                      {plugin.features.slice(0, 3).map((feature, index) => (
                        <span key={index} className="px-3 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium">
                          {feature}
                        </span>
                      ))}
                      {plugin.features.length > 3 && (
                        <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                          +{plugin.features.length - 3} mere
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <Link
                    href={`/plugins/${plugin.id}`}
                    className="w-full group relative overflow-hidden rounded-xl font-bold transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/30 hover:scale-105 text-center"
                  >
                    {/* Enhanced prison bars background effect */}
                    <div className={`absolute inset-0 bg-gradient-to-r from-blue-900 via-purple-900 to-gray-900 opacity-95 group-hover:from-blue-800 group-hover:via-purple-800 group-hover:to-gray-800 transition-all duration-500`}>
                      {/* Enhanced animated prison bars with glow effect */}
                      <div className="absolute inset-0 flex items-center justify-center">
                        {[...Array(10)].map((_, i) => (
                          <div
                            key={i}
                            className="absolute w-1.5 h-full bg-gradient-to-b from-gray-400/40 via-gray-300/60 to-gray-400/40 group-hover:from-blue-400/40 group-hover:via-cyan-300/60 group-hover:to-blue-400/40 transition-all duration-500 shadow-lg group-hover:shadow-blue-500/20"
                            style={{ left: `${i * 10}%` }}
                          >
                            <div className="absolute top-0 w-full h-2 bg-gradient-to-b from-gray-300/60 to-transparent group-hover:from-blue-300/60"></div>
                            <div className="absolute bottom-0 w-full h-2 bg-gradient-to-t from-gray-300/60 to-transparent group-hover:from-blue-300/60"></div>
                          </div>
                        ))}
                      </div>
                      
                      {/* Subtle grid pattern overlay */}
                      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.03)_1px,transparent_1px)] bg-[size:20px_20px] opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
                    </div>
                    
                    {/* Enhanced button content */}
                    <div className="relative z-10 bg-gradient-to-r from-blue-800/80 to-purple-800/80 backdrop-blur-sm px-8 py-4 border border-blue-500/20 group-hover:border-blue-400/40 transition-all duration-500">
                      <div className="flex items-center justify-center gap-3">
                        {/* Enhanced prison icon with animation */}
                        <div className="w-7 h-7 flex items-center justify-center transform transition-all duration-500 group-hover:scale-110 group-hover:rotate-6">
                          <svg className="text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 13.047 14.01c-.04.27-.25.47-.52.49h-1.04c-.27-.02-.48-.22-.52-.49L10.854 7.2l1.18-4.456A1 1 0 0112 2zm-2 8a1 1 0 011 1v1h1a1 1 0 110 2h-1v1a1 1 0 11-2 0v-1H9a1 1 0 110-2h1v-1a1 1 0 011-1z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-white text-lg font-semibold tracking-wide">Åbn Plugin</span>
                        {/* Enhanced arrow with glow effect */}
                        <svg className="w-5 h-5 text-white transform transition-all duration-500 group-hover:translate-x-1.5 drop-shadow-lg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      </div>
                    </div>
                    
                    {/* Enhanced hover overlay effect with particles */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-600/30 via-purple-600/30 to-cyan-600/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    
                    {/* Particle effects on hover */}
                    <div className="absolute inset-0 overflow-hidden opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      {[...Array(6)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-1 h-1 bg-white rounded-full animate-pulse"
                          style={{
                            left: `${Math.random() * 100}%`,
                            top: `${Math.random() * 100}%`,
                            animationDelay: `${i * 0.2}s`,
                            animationDuration: `${1.5 + Math.random() * 1}s`
                          }}
                        ></div>
                      ))}
                    </div>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-flex items-center gap-2 rounded-full border border-green-200 bg-green-50 px-4 py-2 text-sm font-medium text-green-700 mb-4">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
              Kunderne Siger
            </span>
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              <span className="bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                Erfaringer
              </span>
              <br />
              <span className="text-gray-800">Fra Vores Fællesskab</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Lær hvordan vores plugins har forbedret andre servere
            </p>
          </div>

          <div className="text-center py-12">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-yellow-50 rounded-full border border-yellow-200 mb-4">
              <svg className="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium text-yellow-800">Venter på anmeldelser</span>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Vi venter endnu på de første anmeldelser fra vores kunder. Hvis du har brugt vores plugins,
              vil vi meget gerne høre om din erfaring!
            </p>
          </div>
        </div>
      </section>

      {/* Modern Contact Section */}
      <section className="py-20 bg-gradient-to-r from-blue-900 via-blue-800 to-indigo-900 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 rounded-3xl blur-3xl"></div>
            <div className="relative bg-white/10 backdrop-blur-sm rounded-3xl p-8 md:p-12 border border-white/20">
              <div className="mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                  <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                  </svg>
                </div>
                <h2 className="text-3xl sm:text-4xl font-bold mb-4">
                  Har du brug for hjælp?
                </h2>
                <p className="text-xl text-blue-100">
                  Kontakt mig for support, spørgsmål eller tilpassede løsninger
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Link
                  href="/contact"
                  className="group relative bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-cyan-500/30 hover:scale-105"
                >
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    Kontakt Mig
                    <svg className="w-5 h-5 transform transition-all duration-500 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </Link>
                
                <Link href="/support" className="group relative bg-transparent border-2 border-white/30 text-white px-8 py-4 rounded-xl font-bold text-lg overflow-hidden transition-all duration-500 hover:bg-white hover:text-blue-900">
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    Support Center
                    <svg className="w-5 h-5 transform transition-all duration-500 group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </span>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
