import Link from "next/link";
import { notFound } from "next/navigation";

interface Plugin {
  id: string;
  name: string;
  description: string;
  version: string;
  mcVersion: string;
  features: string[];
  requirements: string[];
  downloadUrl: string;
}

const plugins: Record<string, Plugin> = {
  cells: {
    id: "cells",
    name: "Prison Cells",
    description: "Et plugin til at administrere fængselsceller på din Minecraft server. Perfekt til prison servere! Plugin'en giver dig mulighed for at oprette, administrere og tildele fængselsceller til spillere.",
    version: "1.0.0",
    mcVersion: "1.8",
    features: [
      "Automatisk celle oprettelse",
      "Spiller administration",
      "Permission integration",
      "Database understøttelse",
      "Konfigurerbare indstillinger",
      "Web dashboard integration"
    ],
    requirements: [
      "Minecraft Server 1.8",
      "Java 8 eller nyere",
      "Et understøttet database system (MySQL/SQLite)"
    ],
    downloadUrl: "/downloads/cells-1.0.0.jar"
  }
};

export default async function PluginDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const plugin = plugins[id];

  if (!plugin) {
    notFound();
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <Link
          href="/"
          className="text-blue-600 hover:text-blue-800 mb-4 inline-block"
        >
          ← Tilbage til plugins
        </Link>
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="h-64 bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500 text-xl">Prison Cells Plugin Preview</span>
          </div>
          <div className="p-8">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-3xl font-bold text-gray-900">{plugin.name}</h1>
              <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm font-medium rounded">
                v{plugin.version}
              </span>
            </div>
            <p className="text-gray-600 mb-6">{plugin.description}</p>
            
            <div className="flex items-center space-x-4 mb-6">
              <span className="px-3 py-1 bg-green-100 text-green-800 text-sm font-medium rounded">
                Minecraft {plugin.mcVersion}
              </span>
              <span className="px-3 py-1 bg-purple-100 text-purple-800 text-sm font-medium rounded">
                Prison Plugin
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Features</h2>
          <ul className="space-y-2">
            {plugin.features.map((feature, index) => (
              <li key={index} className="flex items-start">
                <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span className="text-gray-700">{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Krav</h2>
          <ul className="space-y-2">
            {plugin.requirements.map((requirement, index) => (
              <li key={index} className="flex items-start">
                <svg className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <span className="text-gray-700">{requirement}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Download Plugin</h2>
        <div className="flex flex-col sm:flex-row gap-4">
          <a
            href={plugin.downloadUrl}
            download
            className="bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium text-center"
          >
            Download Plugin (.jar)
          </a>
          <Link
            href={`/plugins/${plugin.id}/changelog`}
            className="bg-gray-200 text-gray-800 py-3 px-6 rounded-md hover:bg-gray-300 transition-colors duration-200 font-medium text-center"
          >
            Changelog
          </Link>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Få en licens</h2>
        <p className="text-gray-600 mb-4">
          For at få fuld adgang til alle features og opdateringer skal du have en licens til plugin&apos;en.
        </p>
        <button className="bg-green-600 text-white py-3 px-6 rounded-md hover:bg-green-700 transition-colors duration-200 font-medium">
          Få License
        </button>
      </div>
    </div>
  );
}