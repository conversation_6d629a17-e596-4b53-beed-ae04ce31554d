import { License } from "@/types/license";

interface LicenseCardProps {
  license: License;
  onDelete: (licenseId: string) => void;
}

export default function LicenseCard({ license, onDelete }: LicenseCardProps) {
  const getStatusColor = (status: License["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "expired":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="rounded-xl border border-blue-100 bg-white text-gray-800 shadow-lg shadow-blue-50/50 transition-all duration-300 hover:shadow-xl hover:shadow-blue-100/50 hover:border-blue-200 backdrop-blur-sm">
      <div className="flex items-start justify-between mb-4 p-6 pb-4">
        <div>
          <h3 className="text-xl font-bold leading-tight tracking-tight text-gray-800 transition-colors duration-300">
            {license.pluginName}
          </h3>
          <p className="text-sm text-gray-500">Plugin ID: {license.pluginId}</p>
        </div>
        <span className={`px-2.5 py-0.5 text-xs font-semibold rounded-full border ${getStatusColor(license.status)}`}>
          {license.status === "active" ? "AKTIV" : license.status}
        </span>
      </div>
      
      <div className="mb-4 px-6 pt-0">
        <p className="text-sm font-medium text-gray-700 mb-1">License Key:</p>
        <p className="text-sm font-mono text-gray-900 bg-gray-50 p-2 rounded-lg">
          {license.licenseKey}
        </p>
      </div>
      
      <div className="grid grid-cols-2 gap-4 mb-4 px-6 pt-0">
        <div>
          <p className="text-sm font-medium text-gray-700 mb-1">Oprettet:</p>
          <p className="text-sm text-gray-900">{license.createdAt}</p>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-700 mb-1">Udløber:</p>
          <p className="text-sm text-gray-900">{license.expiresAt}</p>
        </div>
      </div>
      
      <div className="flex justify-end p-6 pt-0">
        <button
          onClick={() => onDelete(license.id)}
          className="px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors duration-200"
        >
          Slet License
        </button>
      </div>
    </div>
  );
}