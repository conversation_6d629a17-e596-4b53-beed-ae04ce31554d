import { useState } from "react";

interface Plugin {
  id: string;
  name: string;
}

interface CreateLicenseModalProps {
  plugins: Plugin[];
  isOpen: boolean;
  onClose: () => void;
  onCreate: (pluginId: string) => void;
  isLoading?: boolean;
}

export default function CreateLicenseModal({ 
  plugins, 
  isOpen, 
  onClose, 
  onCreate, 
  isLoading = false 
}: CreateLicenseModalProps) {
  const [selectedPlugin, setSelectedPlugin] = useState("");

  const handleSubmit = () => {
    if (selectedPlugin) {
      onCreate(selectedPlugin);
      setSelectedPlugin("");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center">
      <div className="ff-modal-panel relative bg-white rounded-xl border border-blue-100 shadow-xl shadow-blue-50/50 w-full max-w-md mx-4">
        <div className="p-6">
          <h3 className="text-xl font-bold leading-tight tracking-tight text-gray-800 transition-colors duration-300 mb-4">Opret Ny License</h3>
          <div className="mb-4">
            <label htmlFor="plugin" className="block text-sm font-medium text-gray-700 mb-2">
              Vælg Plugin
            </label>
            <select
              id="plugin"
              value={selectedPlugin}
              onChange={(e) => setSelectedPlugin(e.target.value)}
              className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"
              disabled={isLoading}
            >
              <option value="">Vælg et plugin</option>
              {plugins.map((plugin) => (
                <option key={plugin.id} value={plugin.id}>
                  {plugin.name}
                </option>
              ))}
            </select>
          </div>
          <div className="text-sm text-gray-600 mb-4">
            Bemærk: Du kan kun have én license ad gangen.
          </div>
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              disabled={isLoading}
              className="group relative bg-gradient-to-b from-gray-100 to-gray-200 text-gray-900 px-6 py-3 rounded-lg font-bold overflow-hidden transition-all duration-500 hover:from-gray-200 hover:to-gray-300 hover:shadow-xl hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Annuller
            </button>
            <button
              onClick={handleSubmit}
              disabled={isLoading || !selectedPlugin}
              className="group relative bg-gradient-to-b from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg font-bold overflow-hidden transition-all duration-500 hover:from-blue-700 hover:to-blue-800 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? "Opretter..." : "Opret License"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}