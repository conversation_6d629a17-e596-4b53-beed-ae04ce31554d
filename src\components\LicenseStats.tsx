import { License } from "@/types/license";

interface LicenseStatsProps {
  licenses: License[];
}

export default function LicenseStats({ licenses }: LicenseStatsProps) {
  const activeLicenses = licenses.filter(l => l.status === "active" || l.status === "AKTIV").length;
  const expiredLicenses = licenses.filter(l => l.status === "expired").length;
  const pendingLicenses = licenses.filter(l => l.status === "pending").length;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
      <div className="group relative h-full">
        <div className="rounded-xl border border-blue-100 bg-white text-gray-800 shadow-lg shadow-blue-50/50 transition-all duration-300 hover:shadow-xl hover:shadow-blue-100/50 hover:border-blue-200 backdrop-blur-sm h-full">
          <div className="flex items-center p-6">
            <div className="grid h-12 w-12 place-items-center rounded-xl bg-green-600 text-white transition-all duration-300 group-hover:scale-105">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Aktive Licenser</p>
              <p className="text-2xl font-bold leading-tight tracking-tight text-gray-800 transition-colors duration-300">{activeLicenses}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="group relative h-full">
        <div className="rounded-xl border border-blue-100 bg-white text-gray-800 shadow-lg shadow-blue-50/50 transition-all duration-300 hover:shadow-xl hover:shadow-blue-100/50 hover:border-blue-200 backdrop-blur-sm h-full">
          <div className="flex items-center p-6">
            <div className="grid h-12 w-12 place-items-center rounded-xl bg-red-600 text-white transition-all duration-300 group-hover:scale-105">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Udløbet</p>
              <p className="text-2xl font-bold leading-tight tracking-tight text-gray-800 transition-colors duration-300">{expiredLicenses}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="group relative h-full">
        <div className="rounded-xl border border-blue-100 bg-white text-gray-800 shadow-lg shadow-blue-50/50 transition-all duration-300 hover:shadow-xl hover:shadow-blue-100/50 hover:border-blue-200 backdrop-blur-sm h-full">
          <div className="flex items-center p-6">
            <div className="grid h-12 w-12 place-items-center rounded-xl bg-yellow-600 text-white transition-all duration-300 group-hover:scale-105">
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Afventer</p>
              <p className="text-2xl font-bold leading-tight tracking-tight text-gray-800 transition-colors duration-300">{pendingLicenses}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}