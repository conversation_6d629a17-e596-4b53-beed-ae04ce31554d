{"name": "myck<PERSON>plugins", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.16.2", "@types/mongodb": "^4.0.6", "framer-motion": "^12.23.22", "mongodb": "^6.20.0", "next": "15.5.4", "next-auth": "^4.24.11", "prisma": "^6.16.2", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "tailwindcss": "^4", "typescript": "^5"}}